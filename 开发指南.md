# PyramidStore爬虫插件开发完整指南

## 项目核心理解

### 项目架构
PyramidStore是一个专门用于影视APP数据源的Python爬虫框架，采用插件化架构设计：

**核心组件：**
- `base/spider.py` - 抽象基类，定义标准接口
- `plugin/` - 插件目录，按类型分类（app/、html/、adult/、official/）
- 每个插件继承Spider基类，实现标准化的数据获取方法

**标准接口方法：**
```python
def homeContent(self, filter)          # 首页分类和推荐视频
def categoryContent(self, tid, pg, filter, extend)  # 分类列表
def detailContent(self, ids)           # 视频详情页
def searchContent(self, key, quick, pg) # 搜索功能
def playerContent(self, flag, id, vipFlags) # 播放地址解析
```

## 环境配置

### 项目路径
```
工作目录: d:\augment-projects\py2\PyramidStore-18-main
虚拟环境: venv/
运行方式: 在plugin目录下执行插件
```

### 虚拟环境设置
```bash
python -m venv venv
venv\Scripts\activate
pip install requests lxml pycryptodome pyquery
```

### 严格依赖约束
**仅允许使用以下模块，禁止引入任何新依赖：**
- **标准库**: re, os, json, time, sys, abc, importlib.machinery, base64, urllib.parse, email.utils, colorsys, random
- **第三方库**: requests, lxml, pycryptodome, pyquery

## 插件开发模板

### 基础结构
```python
# -*- coding: utf-8 -*-
# 插件名称 - 网站描述
import re, json, time, sys
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def init(self, extend=""): pass
    def getName(self): return "插件名称"
    
    host = '网站域名'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
    
    # 编码修复函数（必备）
    def fix_encoding(self, text):
        if not text:
            return text
        try:
            garbled_patterns = ['\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7']
            has_garbled = any(pattern in text for pattern in garbled_patterns)
            if has_garbled:
                fixed = text.encode('latin1').decode('utf-8')
                if re.search(r'[\u4e00-\u9fff]', fixed):
                    return fixed
            return text
        except:
            return text
    
    # 统一请求方法（必备）
    def fetch_with_encoding(self, url, **kwargs):
        try:
            response = self.fetch(url, **kwargs)
            response.encoding = 'utf-8'
            return response
        except Exception as e:
            self.log(f"请求失败: {e}")
            raise
```

## 标准化开发流程

### 1. 网站结构分析
```python
# 分析脚本模板
import requests
from pyquery import PyQuery as pq

def analyze_website(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
    response = requests.get(url, headers=headers)
    response.encoding = 'utf-8'
    doc = pq(response.text)
    
    # 分析关键元素
    print(f"状态码: {response.status_code}")
    print(f"页面标题: {doc('title').text()}")
    
    # 查找视频容器
    video_selectors = ['.module-item', '.video-item', '.movie-item', '.card']
    for selector in video_selectors:
        items = doc(selector)
        if items:
            print(f"视频容器: {selector} ({len(items)}个)")
            break
    
    # 分析第一个视频项结构
    if items:
        first_item = items.eq(0)
        print(f"HTML结构: {first_item.outer_html()}")
        print(f"标题属性: {first_item.attr('title')}")
        print(f"链接属性: {first_item.attr('href')}")
```

### 2. URL模式识别
**常见模式：**
- 主页: `/`
- 分类: `/t/{分类ID}/` 或 `/category/{分类ID}/`
- 详情: `/detail/{视频ID}/` 或 `/video/{视频ID}/`
- 搜索: `/search?wd={关键词}` 或 `/s/{特殊路径}/?wd={关键词}`
- 播放: `/play/{视频ID}-{播放源}-{集数}/`

### 3. 数据格式标准

**视频列表项：**
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题", 
    "vod_pic": "封面图片URL",
    "vod_year": "年份",
    "vod_remarks": "备注信息"
}
```

**视频详情：**
```json
{
    "vod_id": "视频ID",
    "vod_name": "视频标题",
    "vod_pic": "封面图片URL",
    "vod_actor": "演员",
    "vod_director": "导演", 
    "vod_content": "简介",
    "vod_play_from": "播放源1$$$播放源2",
    "vod_play_url": "剧集1$url1#剧集2$url2$$$剧集1$url1#剧集2$url2"
}
```

## 常见问题解决方案

### 1. 图片懒加载问题
```python
# 优先获取真实图片URL，避免占位图
pic = img_elem.attr('data-original') or img_elem.attr('data-src') or img_elem.attr('src') or ''
if pic and not pic.startswith('http'):
    pic = self.host + pic if pic.startswith('/') else ''
```

### 2. 编码乱码问题
```python
def fix_encoding(self, text):
    """修复UTF-8编码问题"""
    if not text:
        return text
    try:
        # 检查乱码特征
        garbled_patterns = ['\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7', '\u00e8', '\u00e9']
        has_garbled = any(pattern in text for pattern in garbled_patterns)
        
        if has_garbled:
            # Latin1->UTF-8转换
            fixed = text.encode('latin1').decode('utf-8')
            if re.search(r'[\u4e00-\u9fff]', fixed):
                return fixed
        return text
    except:
        return text

# 在所有文本解析处应用
title = self.fix_encoding(title_elem.text())
content = self.fix_encoding(info_elem.text())
```

### 3. 搜索URL格式问题
```python
# 需要实际分析网站的搜索接口，不要假设格式
# 常见错误：假设是/search，实际可能是/s/特殊路径/
def analyze_search_url(self, keyword="测试"):
    test_patterns = [
        f"{self.host}/search?wd={quote(keyword)}",
        f"{self.host}/s/?wd={quote(keyword)}",
        f"{self.host}/s/-------------/?wd={quote(keyword)}",
    ]
    
    for url in test_patterns:
        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                print(f"有效搜索URL: {url}")
                return url.split('?')[0]
        except:
            continue
```

### 4. HTML结构差异处理
```python
# 不同页面可能有不同结构，需要分别处理
def parse_video_item(self, item, page_type="category"):
    if page_type == "category":
        # 分类页面：.module-item本身是<a>标签
        title = item.attr('title')
        href = item.attr('href')
    elif page_type == "search":
        # 搜索页面：.module-item是<div>，链接在内部
        detail_links = item.find('a[href*="/detail/"]')
        if detail_links:
            detail_link = detail_links.eq(0)
            href = detail_link.attr('href')
            title = detail_link.find('strong').text()
```

## 测试验证流程

### 功能测试脚本
```python
def test_spider():
    spider = Spider()
    spider.init()
    
    print("=== 测试首页内容 ===")
    home_result = spider.homeContent({})
    classes = home_result.get('class', [])
    videos = home_result.get('list', [])
    print(f"分类数量: {len(classes)}")
    print(f"首页视频数量: {len(videos)}")
    
    print("=== 测试分类内容 ===")
    if classes:
        category_result = spider.categoryContent(classes[0]['type_id'], '1', {}, {})
        print(f"分类视频数量: {len(category_result.get('list', []))}")
    
    print("=== 测试搜索功能 ===")
    search_result = spider.searchContent('测试', False, '1')
    print(f"搜索结果数量: {len(search_result.get('list', []))}")
    
    print("=== 测试详情页面 ===")
    if videos:
        detail_result = spider.detailContent([videos[0]['vod_id']])
        if detail_result.get('list'):
            detail = detail_result['list'][0]
            print(f"播放源: {detail.get('vod_play_from', '')}")
            print(f"播放列表长度: {len(detail.get('vod_play_url', ''))}")
```

## 质量保证要点

### 1. 错误处理
```python
# 每个方法都要有完善的异常处理
def someMethod(self):
    try:
        # 主要逻辑
        return success_result
    except Exception as e:
        self.log(f"方法执行失败: {e}")
        return default_result
```

### 2. 日志记录
```python
# 使用self.log()记录关键信息
self.log("开始解析详情页")
self.log(f"找到 {len(items)} 个视频项")
self.log(f"解析视频: {title}")
```

### 3. 数据清理
```python
# 统一的数据清理
title = title.strip() if title else ''
if pic and not pic.startswith('http'):
    pic = self.host + pic if pic.startswith('/') else ''
```

## 泥视频插件经验总结

### 成功解决的问题
1. **图片匹配**: 优先获取`data-original`属性
2. **编码乱码**: 智能检测和Latin1->UTF-8转换
3. **搜索URL**: 实际分析得出`/s/-------------/`格式
4. **结构差异**: 搜索页面和分类页面HTML结构不同

### 关键技术点
- 编码修复函数必备
- 统一的请求方法
- 多种图片属性获取策略
- 不同页面结构的适配逻辑

## 开发检查清单

- [ ] 网站结构分析完成
- [ ] URL模式识别正确
- [ ] 编码修复函数已添加
- [ ] 图片懒加载处理
- [ ] 搜索URL格式验证
- [ ] 所有方法异常处理
- [ ] 功能测试通过
- [ ] 数据格式符合标准
- [ ] 日志记录完善
- [ ] 文档编写完整

使用这个指南，您可以直接开始下一个插件的开发工作，避免重复踩坑，提高开发效率。
